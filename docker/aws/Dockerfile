FROM ubuntu:22.04

ARG DOCKER_HOST_VALUE=tcp://docker:2375
ENV DOCKER_HOST=${DOCKER_HOST_VALUE}

RUN apt update;

RUN apt-get install -y ca-certificates curl rsync && \
    install -m 0755 -d /etc/apt/keyrings && \
    curl -fsSL https://download.docker.com/linux/ubuntu/gpg -o /etc/apt/keyrings/docker.asc && \
    chmod a+r /etc/apt/keyrings/docker.asc && \
    echo \
      "deb [arch=$(dpkg --print-architecture) signed-by=/etc/apt/keyrings/docker.asc] https://download.docker.com/linux/ubuntu \
      $(. /etc/os-release && echo "${UBUNTU_CODENAME:-$VERSION_CODENAME}") stable" | \
      tee /etc/apt/sources.list.d/docker.list > /dev/null && \
    apt-get update

RUN apt-get install -y openssh-server docker-ce docker-ce-cli containerd.io docker-buildx-plugin docker-compose-plugin && \
    mkdir /var/run/sshd && \
    echo "root:root" | chpasswd && \
    sed -i 's/#PermitRootLogin.*/PermitRootLogin yes/' /etc/ssh/sshd_config && \
    sed -i 's/#PasswordAuthentication.*/PasswordAuthentication yes/' /etc/ssh/sshd_config

RUN sh -c "$(curl --location https://taskfile.dev/install.sh)" -- -d -b /bin

EXPOSE 22

CMD ["/usr/sbin/sshd", "-D"]
