FROM ubuntu:22.04

ENV DOCKER_HOST=tcp://docker:2375

RUN apt update && apt install -y openssh-server docker.io rsync curl && \
    mkdir /var/run/sshd && \
    echo "root:root" | chpasswd && \
    sed -i 's/#PermitRootLogin.*/PermitRootLogin yes/' /etc/ssh/sshd_config && \
    sed -i 's/#PasswordAuthentication.*/PasswordAuthentication yes/' /etc/ssh/sshd_config

RUN sh -c "$(curl --location https://taskfile.dev/install.sh)" -- -d -b /bin


EXPOSE 22

CMD ["/usr/sbin/sshd", "-D"]
