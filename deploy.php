<?php

namespace Deployer;

require 'recipe/common.php';

task('deploy:update_code', function() {
  upload(__DIR__ . '/', '{{release_path}}', [
    'options' => [
      '--exclude=html/vendor/',
      '--exclude=html/vendor',
    ]
  ]);
});

task('deploy:build', function() {
  run('cd {{deploy_path}} && task build');
});

host('aws')
  ->set('deploy_path', '/root/starterkit')
  ->setRemoteUser('root');
