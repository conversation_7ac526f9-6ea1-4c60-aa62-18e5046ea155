<?php

namespace Deployer;

require 'recipe/common.php';

// Configure shared directories
set('shared_dirs', ['env']);

task('deploy:update_code', function() {
  upload(__DIR__ . '/compose.yml', '{{release_path}}/compose.yml');
  upload(__DIR__ . '/taskfile.yml', '{{release_path}}/taskfile.yml');
});

task('deploy:build', function() {
  run('cd {{release_path}} && task build');
});

task('deploy:up', function() {
  run('cd {{release_path}} && task up');
});

// Add our custom tasks to the deploy flow using hooks
after('deploy:symlink', 'deploy:build');
after('deploy:build', 'deploy:up');

host('aws')
  ->set('deploy_path', '/root/starterkit')
  ->setRemoteUser('root');
