<?php

namespace Deployer;

require 'recipe/common.php';

task('deploy:update_code', function() {
  upload(__DIR__ . '/compose.yml', '{{release_path}}/compose.yml');
  upload(__DIR__ . '/taskfile.yml', '{{release_path}}/taskfile.yml');
});

task('deploy:build', function() {
  run('cd {{deploy_path}} && task build');
});

host('aws')
  ->set('deploy_path', '/root/starterkit')
  ->setRemoteUser('root');
