<?php

namespace Deployer;

require 'recipe/common.php';

task('deploy:update_code', function() {
  upload(__DIR__ . '/compose.yml', '{{deploy_path}}/compose.yml');
  upload(__DIR__ . '/taskfile.yml', '{{deploy_path}}/taskfile.yml');
});

task('deploy:build', function() {
  run('cd {{deploy_path}} && task build');
});

task('deploy:up', function() {
  run('cd {{deploy_path}} && task up');
});

// Add our custom tasks to the deploy flow using hooks
after('deploy:update_code', 'deploy:build');
after('deploy:build', 'deploy:up');

host('aws')
  ->set('deploy_path', '/root/starterkit')
  ->setRemoteUser('root');
